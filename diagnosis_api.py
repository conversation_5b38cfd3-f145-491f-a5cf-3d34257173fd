#!/usr/bin/env python3
"""
REST API for Medical Diagnosis Chat System
Provides endpoints for interactive medical diagnosis using vector search
"""

import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import uuid

# Import classes from the existing diagnosis_chat.py
from diagnosis_chat import Diagnosis<PERSON><PERSON>, OpenAIEmbedder, DiagnosisDatabase, OpenAILLM

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend integration

# In-memory session storage (in production, use Redis or database)
chat_sessions = {}
SESSION_TIMEOUT = timedelta(hours=1)  # Sessions expire after 1 hour

class ChatSession:
    """Represents a chat session with state management"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.symptoms_history = []
        self.previous_questions = []
        self.conversation_history = []  # Track question-answer pairs
        self.excluded_diagnoses = set()
        self.question_count = 0
        self.max_questions = 5
        self.current_diagnoses = []
        self.is_completed = False
        
        # Initialize diagnosis components
        self.embedder = OpenAIEmbedder()
        self.database = DiagnosisDatabase()
        self.llm = OpenAILLM()
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
    
    def is_expired(self) -> bool:
        """Check if session has expired"""
        return datetime.now() - self.last_activity > SESSION_TIMEOUT
    
    def add_symptoms(self, symptoms: str):
        """Add symptoms to history"""
        self.symptoms_history.append(symptoms)
        self.update_activity()
    
    def add_question(self, question: str):
        """Add question to history"""
        self.previous_questions.append(question)
        self.update_activity()
    
    def get_combined_symptoms(self) -> str:
        """Get all symptoms combined"""
        return " ".join(self.symptoms_history)

def cleanup_expired_sessions():
    """Remove expired sessions from memory"""
    expired_sessions = [
        session_id for session_id, session in chat_sessions.items()
        if session.is_expired()
    ]
    for session_id in expired_sessions:
        del chat_sessions[session_id]
    
    if expired_sessions:
        logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

def get_or_create_session(session_id: str = None) -> ChatSession:
    """Get existing session or create new one"""
    cleanup_expired_sessions()
    
    if session_id and session_id in chat_sessions:
        session = chat_sessions[session_id]
        session.update_activity()
        return session
    
    # Create new session
    new_session_id = session_id or str(uuid.uuid4())
    session = ChatSession(new_session_id)
    chat_sessions[new_session_id] = session
    logger.info(f"Created new chat session: {new_session_id}")
    return session

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        database = DiagnosisDatabase()
        diagnosis_count = database.get_diagnosis_count()
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database_diagnoses': diagnosis_count,
            'active_sessions': len(chat_sessions)
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@app.route('/api/chat/start', methods=['POST'])
def start_chat():
    """Start a new chat session"""
    try:
        data = request.get_json() or {}
        initial_symptoms = data.get('symptoms', '').strip()
        
        if not initial_symptoms:
            return jsonify({
                'error': 'Initial symptoms are required'
            }), 400
        
        # Create new session
        session = get_or_create_session()
        session.add_symptoms(initial_symptoms)

        # Add initial symptoms to conversation history
        session.conversation_history.append({
            "step": 1,
            "type": "initial_symptoms",
            "content": initial_symptoms
        })
        
        # Process initial symptoms
        result = process_symptoms_for_session(session)
        
        response = {
            'session_id': session.session_id,
            'message': 'Chat session started successfully',
            **result
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Error starting chat: {e}")
        return jsonify({
            'error': 'Failed to start chat session'
        }), 500

@app.route('/api/chat/<session_id>/answer', methods=['POST'])
def answer_question(session_id: str):
    """Answer a follow-up question"""
    try:
        if session_id not in chat_sessions:
            return jsonify({
                'error': 'Session not found or expired'
            }), 404
        
        session = chat_sessions[session_id]
        data = request.get_json() or {}
        answer = data.get('answer', '').strip()
        
        if not answer:
            return jsonify({
                'error': 'Answer is required'
            }), 400
        
        # Add answer to symptoms history
        session.add_symptoms(answer)

        # Add answer to conversation history
        session.conversation_history.append({
            "step": len(session.conversation_history) + 1,
            "type": "answer",
            "content": answer,
            "question_number": session.question_count
        })

        # Analyze response and filter diagnoses
        if session.current_diagnoses and session.previous_questions:
            last_question = session.previous_questions[-1]
            analyze_response_and_filter(session, answer, session.current_diagnoses, last_question)
        
        # Process symptoms with new information
        result = process_symptoms_for_session(session)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing answer: {e}")
        return jsonify({
            'error': 'Failed to process answer'
        }), 500

@app.route('/api/chat/<session_id>/status', methods=['GET'])
def get_chat_status(session_id: str):
    """Get current chat session status"""
    try:
        if session_id not in chat_sessions:
            return jsonify({
                'error': 'Session not found or expired'
            }), 404
        
        session = chat_sessions[session_id]
        
        return jsonify({
            'session_id': session.session_id,
            'question_count': session.question_count,
            'max_questions': session.max_questions,
            'is_completed': session.is_completed,
            'symptoms_history': session.symptoms_history,
            'previous_questions': session.previous_questions,
            'conversation_history': session.conversation_history,
            'current_diagnoses': session.current_diagnoses[:5] if session.current_diagnoses else [],
            
        })
        
    except Exception as e:
        logger.error(f"Error getting chat status: {e}")
        return jsonify({
            'error': 'Failed to get chat status'
        }), 500

def convert_to_chat_format(conversation_history: List[Dict]) -> List[Dict]:
    """Convert conversation history to chat format similar to the provided example"""
    if not conversation_history:
        return []

    chat_format = []
    for step in conversation_history:
        # Check if already in chat format (has 'role' field)
        if step.get('role'):
            # Already in chat format, just return as-is
            chat_format.append(step)
        # Convert from simple format (has 'type' field)
        elif step.get('type') == 'initial_symptoms':
            chat_format.append({
                "role": "user",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })
        elif step.get('type') == 'question':
            chat_format.append({
                "role": "assistant",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "step-start"
                    },
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })
        elif step.get('type') == 'answer':
            chat_format.append({
                "role": "user",
                "content": step.get('content', ''),
                "id": str(uuid.uuid4()),
                "createdAt": datetime.now().isoformat() + "Z",
                "parts": [
                    {
                        "type": "text",
                        "text": step.get('content', '')
                    }
                ]
            })

    return chat_format

def extract_symptoms_from_question(question: str) -> List[str]:
    """Extract specific symptoms mentioned in a follow-up question"""
    import re

    question_lower = question.lower()
    symptoms = []

    # Common patterns for symptoms in questions
    symptom_patterns = [
        r'such as ([^?]+)',  # "such as cough or difficulty breathing"
        r'including ([^?]+)',  # "including fever or chills"
        r'like ([^?]+)',  # "like vomiting or diarrhea"
        r'any ([^,?]+)',  # "any pain or discomfort"
    ]

    for pattern in symptom_patterns:
        matches = re.findall(pattern, question_lower)
        for match in matches:
            # Split by 'or', 'and', commas and clean up
            symptom_parts = re.split(r'\s+or\s+|\s+and\s+|,', match)
            for part in symptom_parts:
                clean_symptom = part.strip()
                if clean_symptom and len(clean_symptom) > 2:
                    symptoms.append(clean_symptom)

    # Also look for direct symptom mentions
    direct_symptoms = [
        'cough', 'fever', 'breathing', 'respiratory', 'temperature',
        'pain', 'swelling', 'discharge', 'inflammation', 'lethargy',
        'weakness', 'diarrhea', 'vomiting', 'appetite', 'eating',
        'drinking', 'lameness', 'walking', 'movement'
    ]

    for symptom in direct_symptoms:
        if symptom in question_lower and symptom not in symptoms:
            symptoms.append(symptom)

    return symptoms

def extract_symptoms_summary(conversation_history: List[Dict]) -> str:
    """Extract a clean summary of current symptoms from conversation history"""
    if not conversation_history:
        return "No symptoms provided"

    # Get initial symptoms and additional info from chat format or simple format
    initial_symptoms = ""
    additional_info = []

    for step in conversation_history:
        # Handle both chat format and simple format
        if isinstance(step, dict):
            # Chat format (with role, content, parts)
            if step.get('role') == 'user':
                content = step.get('content', '')
                # First user message is initial symptoms
                if not initial_symptoms and content:
                    initial_symptoms = content
                elif content and not content.lower().startswith('no'):
                    additional_info.append(content)
            # Simple format (with type)
            elif step.get('type') == 'initial_symptoms':
                initial_symptoms = step.get('content', '')
            elif step.get('type') == 'answer':
                answer = step.get('content', '').strip()
                if answer and not answer.lower().startswith('no'):
                    additional_info.append(answer)

    # Combine initial symptoms with relevant additional information
    if additional_info:
        return f"{initial_symptoms}. Additional findings: {'; '.join(additional_info)}"
    else:
        return initial_symptoms

def create_enhanced_symptoms_summary(conversation_history: List[Dict]) -> str:
    """Create an enhanced symptoms summary that includes inferred symptoms from positive answers"""
    if not conversation_history:
        return "No symptoms provided"

    # Start with initial symptoms
    initial_symptoms = ""
    enhanced_symptoms = []
    current_question = ""

    for entry in conversation_history:
        if entry.get('type') == 'initial_symptoms':
            initial_symptoms = entry.get('content', '')
            enhanced_symptoms.append(initial_symptoms)
        elif entry.get('role') == 'user' and not initial_symptoms:
            # Handle chat format - first user message is initial symptoms
            initial_symptoms = entry.get('content', '')
            enhanced_symptoms.append(initial_symptoms)
        elif entry.get('type') == 'question':
            # Store the question for the next answer
            current_question = entry.get('content', '')
        elif entry.get('role') == 'assistant':
            # Handle chat format - assistant questions
            current_question = entry.get('content', '')
        elif entry.get('type') == 'answer':
            answer = entry.get('content', '').strip().lower()

            # If user gave a positive answer, extract symptoms from the previous question
            if any(positive in answer for positive in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                if current_question:
                    extracted_symptoms = extract_symptoms_from_question(current_question)
                    enhanced_symptoms.extend(extracted_symptoms)
            # If user gave additional descriptive information (not just yes/no)
            elif answer not in ['yes', 'no', 'none', 'never', 'not', 'negative', 'absent']:
                enhanced_symptoms.append(answer)
        elif entry.get('role') == 'user' and initial_symptoms:
            # Handle chat format - subsequent user messages are answers
            answer = entry.get('content', '').strip().lower()

            # If user gave a positive answer, extract symptoms from the previous question
            if any(positive in answer for positive in ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']):
                if current_question:
                    extracted_symptoms = extract_symptoms_from_question(current_question)
                    enhanced_symptoms.extend(extracted_symptoms)
            # If user gave additional descriptive information (not just yes/no)
            elif answer not in ['yes', 'no', 'none', 'never', 'not', 'negative', 'absent']:
                enhanced_symptoms.append(answer)

    # Remove duplicates while preserving order
    seen = set()
    unique_symptoms = []
    for symptom in enhanced_symptoms:
        if symptom and symptom.lower() not in seen:
            seen.add(symptom.lower())
            unique_symptoms.append(symptom)

    return " ".join(unique_symptoms)

def process_symptoms_for_session(session: ChatSession) -> Dict:
    """Process symptoms for a session and return result"""
    try:
        # Get combined symptoms (keep for backward compatibility)
        combined_symptoms = session.get_combined_symptoms()

        # Get enhanced symptoms summary that includes inferred symptoms from positive answers
        enhanced_symptoms = create_enhanced_symptoms_summary(session.conversation_history)

        # Also get the old symptoms summary for comparison
        symptoms_summary = extract_symptoms_summary(session.conversation_history)

        # Log the different approaches for debugging
        logger.info(f"Combined symptoms: {combined_symptoms}")
        logger.info(f"Old symptoms summary: {symptoms_summary}")
        logger.info(f"Enhanced symptoms summary: {enhanced_symptoms}")

        # Get embedding for enhanced symptoms
        embeddings = session.embedder.get_embeddings([enhanced_symptoms])
        if not embeddings:
            return {
                'error': 'Failed to process symptoms',
                'type': 'processing_error'
            }
        
        # Search for similar diagnoses
        diagnoses = session.database.search_similar_diagnoses(embeddings[0], top_k=10)
        
        # Filter out excluded diagnoses and low confidence ones
        diagnoses = [d for d in diagnoses if d['name'] not in session.excluded_diagnoses]
        diagnoses = [d for d in diagnoses if d['confidence_score'] >= 0.4]
        
        if not diagnoses:
            return {
                'type': 'no_matches',
                'message': 'No matching diagnoses found. Please consult a medical professional.',
                'is_completed': True
            }
        
        session.current_diagnoses = diagnoses
        top_confidence = diagnoses[0]['confidence_score']
        
        # Check if we should provide final diagnosis
        if session.question_count >= session.max_questions or top_confidence > 0.8:
            session.is_completed = True
            return {
                'type': 'final_diagnosis',
                'diagnosis': diagnoses[0],
                'top_diagnoses': diagnoses[:5],
                'is_completed': True,
                'confidence_threshold_met': top_confidence > 0.8,
                'max_questions_reached': session.question_count >= session.max_questions
            }
        
        # Convert conversation history to chat format for LLM
        chat_format_history = convert_to_chat_format(session.conversation_history)

        # Debug logging
        logger.info(f"Session conversation history length: {len(session.conversation_history)}")
        logger.info(f"Chat format history length: {len(chat_format_history)}")
        if session.conversation_history:
            logger.info(f"Last conversation entry: {session.conversation_history[-1]}")

        # Try to generate follow-up question using enhanced symptoms summary
        question = session.llm.generate_follow_up_question(
            enhanced_symptoms, diagnoses, session.previous_questions, chat_format_history
        )
        
        if question is None:
            # No good question available, provide final diagnosis
            session.is_completed = True
            return {
                'type': 'final_diagnosis',
                'diagnosis': diagnoses[0],
                'top_diagnoses': diagnoses[:5],
                'is_completed': True,
                'reason': 'No further differentiating questions available'
            }
        
        # Increment question count and store question
        session.question_count += 1
        session.add_question(question)

        # Add question to conversation history
        session.conversation_history.append({
            "step": len(session.conversation_history) + 1,
            "type": "question",
            "content": question,
            "question_number": session.question_count
        })
        
        return {
            'type': 'follow_up_question',
            'question': question,
            'question_number': session.question_count,
            'max_questions': session.max_questions,
            'top_diagnoses': diagnoses[:5],
            'is_completed': False
        }
        
    except Exception as e:
        logger.error(f"Error processing symptoms: {e}")
        return {
            'error': 'Failed to process symptoms',
            'type': 'processing_error'
        }

def analyze_response_and_filter(session: ChatSession, response: str, diagnoses: List[Dict], question: str):
    """Analyze user response and filter out irrelevant diagnoses"""
    response_lower = response.lower()
    question_lower = question.lower()
    
    # Simple keyword-based filtering
    negative_responses = ['no', 'not', 'none', 'never', 'absent', 'negative']
    positive_responses = ['yes', 'positive', 'present', 'severe', 'mild', 'moderate']
    
    is_negative = any(neg in response_lower for neg in negative_responses)
    is_positive = any(pos in response_lower for pos in positive_responses)
    
    # If user gives a clear negative response, exclude relevant diagnoses
    if is_negative and not is_positive:
        # Special case: if question is about udder/milk and user says no, exclude all mastitis
        if any(term in question_lower for term in ['udder', 'milk', 'mammary']):
            mastitis_diagnoses = [
                'Acute Mastitis', 'Sub Acute Mastitis', 'Mild Mastitis',
                'Gangrenous Mastitis', 'Per Acute Mastitis', 'Chronic Mastitis',
                'Clinical Mastitis', 'Subclinical Mastitis'
            ]
            
            for mastitis_name in mastitis_diagnoses:
                session.excluded_diagnoses.add(mastitis_name)
            
            # Also check for any diagnosis containing mastitis keywords
            mastitis_patterns = ['mastitis', 'mammary', 'udder']
            for diagnosis in diagnoses:
                diagnosis_name_lower = diagnosis['name'].lower()
                if any(keyword in diagnosis_name_lower for keyword in mastitis_patterns):
                    session.excluded_diagnoses.add(diagnosis['name'])
        
        # General keyword-based exclusion
        else:
            question_keywords = extract_keywords_from_question(question)
            
            for diagnosis in diagnoses[:5]:  # Check top 5 diagnoses
                diagnosis_symptoms = diagnosis['symptoms'].lower()
                diagnosis_name = diagnosis['name'].lower()
                diagnosid_id = diagnosis['diagnosis_id']
                # If the diagnosis heavily features the questioned symptom and user says no, exclude it
                keyword_matches = sum(1 for keyword in question_keywords
                                    if keyword in diagnosis_symptoms or keyword in diagnosis_name)
                if keyword_matches >= 1:  # If any keyword matches
                    session.excluded_diagnoses.add(diagnosis['name'])

def extract_keywords_from_question(question: str) -> List[str]:
    """Extract key medical terms from a question"""
    medical_keywords = [
        'respiratory', 'breathing', 'cough', 'fever', 'temperature',
        'udder', 'milk', 'swollen', 'inflammation', 'pain', 'discharge',
        'appetite', 'eating', 'drinking', 'lethargy', 'weakness',
        'diarrhea', 'vomiting', 'skin', 'lesions', 'wounds',
        'lameness', 'walking', 'movement', 'joints', 'legs'
    ]
    
    question_lower = question.lower()
    found_keywords = []
    
    for keyword in medical_keywords:
        if keyword in question_lower:
            found_keywords.append(keyword)
    
    return found_keywords

if __name__ == '__main__':
    port = int(os.getenv('API_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting Diagnosis API on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
